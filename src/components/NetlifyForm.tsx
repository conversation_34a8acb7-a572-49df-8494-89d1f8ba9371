import React, { useState } from 'react';
import { colors } from '../utils/colors';

interface NetlifyFormProps {
  title?: string;
  formName?: string;
  showImageUpload?: boolean;
}

const NetlifyForm: React.FC<NetlifyFormProps> = ({
  title = "Get Your Free Project Estimate",
  formName = "contact",
  showImageUpload = true
}) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    state: '',
    country: '',
    postal_code: '',
    project_image_url: '',
    message: '',
    project_type: '',
    consent: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormStatus('Submitting...');

    try {
      // Create FormData for Netlify submission
      const netlifyFormData = new FormData();
      netlifyFormData.append('form-name', formName);
      
      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        netlifyFormData.append(key, value.toString());
      });

      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(netlifyFormData as any).toString()
      });

      if (response.ok) {
        setFormStatus('Thank you! Your estimate request has been sent.');
        // Reset form
        setFormData({
          first_name: '', last_name: '', phone: '', email: '',
          address: '', city: '', state: '', country: '', postal_code: '',
          project_image_url: '', message: '', project_type: '', consent: false
        });
      } else {
        throw new Error(`Server responded with status: ${response.status}`);
      }
    } catch (error) {
      console.error('Form submission failed:', error);
      setFormStatus('Submission failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-sm bg-primary-600 text-white rounded-2xl shadow-2xl p-6">
      <h2 className="text-xl font-bold text-center mb-5">{title}</h2>
      <form 
        name={formName}
        method="POST" 
        netlify 
        netlify-honeypot="bot-field"
        onSubmit={handleSubmit}
      >
        {/* Hidden field for Netlify */}
        <input type="hidden" name="form-name" value={formName} />
        
        {/* Honeypot field for spam protection */}
        <div style={{ display: 'none' }}>
          <label>Don't fill this out if you're human: <input name="bot-field" /></label>
        </div>

        <div className="space-y-3">
          <input
            type="text"
            name="first_name"
            value={formData.first_name}
            onChange={handleInputChange}
            placeholder="First Name"
            className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
            required
          />
          
          <input
            type="text"
            name="last_name"
            value={formData.last_name}
            onChange={handleInputChange}
            placeholder="Last Name"
            className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
            required
          />
          
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="Phone*"
            className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
            required
          />
          
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Email*"
            className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
            required
          />

          <input
            type="text"
            name="address"
            value={formData.address}
            onChange={handleInputChange}
            placeholder="Street Address"
            className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
          />

          <div className="grid grid-cols-2 gap-2">
            <input
              type="text"
              name="city"
              value={formData.city}
              onChange={handleInputChange}
              placeholder="City"
              className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
            />
            <input
              type="text"
              name="state"
              value={formData.state}
              onChange={handleInputChange}
              placeholder="State"
              className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
            />
          </div>

          <select
            name="project_type"
            value={formData.project_type}
            onChange={handleInputChange}
            className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
          >
            <option value="">Select Project Type</option>
            <option value="deck">Deck Construction</option>
            <option value="bathroom">Bathroom Remodeling</option>
            <option value="repair">Home Repairs</option>
            <option value="handyman">Handyman Services</option>
            <option value="other">Other</option>
          </select>

          {showImageUpload && (
            <div>
              <input
                type="url"
                name="project_image_url"
                value={formData.project_image_url}
                onChange={handleInputChange}
                placeholder="Project Image URL (optional)"
                className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm`}
              />
              <p className="text-xs text-primary-100 mt-1">
                Upload your image to your preferred service and paste the URL here
              </p>
            </div>
          )}

          <textarea
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            placeholder="Tell us about your project..."
            rows={3}
            className={`w-full p-2.5 rounded-lg ${colors.neutral.text.darker} text-sm resize-none`}
          />

          <div className="flex items-start space-x-2">
            <input
              id="consent"
              name="consent"
              type="checkbox"
              checked={formData.consent}
              onChange={handleInputChange}
              className="h-4 w-4 rounded mt-1"
              required
            />
            <label htmlFor="consent" className="text-xs text-primary-100 leading-tight">
              I agree to receive text message updates from Horizon Carpentry. Msg frequency varies. 
              Msg & data rates may apply. Reply STOP to unsubscribe.
            </label>
          </div>
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full ${colors.neutral.bg.darkest} hover:bg-neutral-800 text-white font-bold p-3 rounded-lg mt-5 transition duration-300 disabled:${colors.neutral.bg.main} text-sm`}
        >
          {isSubmitting ? 'Submitting...' : 'Get Started'}
        </button>

        {formStatus && (
          <div className={`text-center mt-3 font-medium text-sm ${formStatus.includes('failed') ? colors.error.text.main : colors.success.text.main}`}>
            {formStatus}
          </div>
        )}
      </form>
    </div>
  );
};

export default NetlifyForm;
