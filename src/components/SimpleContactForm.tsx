import React, { useState } from 'react';
import { colors } from '../utils/colors';

interface SimpleContactFormProps {
  title?: string;
  className?: string;
}

const SimpleContactForm: React.FC<SimpleContactFormProps> = ({
  title = "Contact Us",
  className = ""
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service_interest: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormStatus('Submitting...');

    try {
      // Create FormData for Netlify submission
      const netlifyFormData = new FormData();
      netlifyFormData.append('form-name', 'simple-contact');
      
      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        netlifyFormData.append(key, value);
      });

      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(netlifyFormData as any).toString()
      });

      if (response.ok) {
        setFormStatus('Thank you! We\'ll get back to you soon.');
        // Reset form
        setFormData({
          name: '', email: '', phone: '', service_interest: '', message: ''
        });
      } else {
        throw new Error(`Server responded with status: ${response.status}`);
      }
    } catch (error) {
      console.error('Form submission failed:', error);
      setFormStatus('Submission failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">{title}</h3>
      
      <form 
        name="simple-contact"
        method="POST" 
        netlify 
        netlify-honeypot="bot-field"
        onSubmit={handleSubmit}
      >
        {/* Hidden field for Netlify */}
        <input type="hidden" name="form-name" value="simple-contact" />
        
        {/* Honeypot field for spam protection */}
        <div style={{ display: 'none' }}>
          <label>Don't fill this out if you're human: <input name="bot-field" /></label>
        </div>

        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div>
            <label htmlFor="service_interest" className="block text-sm font-medium text-gray-700 mb-1">
              Service Interest
            </label>
            <select
              id="service_interest"
              name="service_interest"
              value={formData.service_interest}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">Select a service</option>
              <option value="deck">Deck Construction</option>
              <option value="bathroom">Bathroom Remodeling</option>
              <option value="repair">Home Repairs</option>
              <option value="handyman">Handyman Services</option>
              <option value="consultation">Free Consultation</option>
            </select>
          </div>

          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
              Message *
            </label>
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              rows={4}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              placeholder="Tell us about your project or ask any questions..."
              required
            />
          </div>
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full mt-6 ${colors.primary.bg.main} hover:${colors.primary.bg.dark} text-white font-bold py-3 px-6 rounded-lg transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {isSubmitting ? 'Sending...' : 'Send Message'}
        </button>

        {formStatus && (
          <div className={`text-center mt-4 font-medium ${formStatus.includes('failed') ? 'text-red-600' : 'text-green-600'}`}>
            {formStatus}
          </div>
        )}
      </form>
    </div>
  );
};

export default SimpleContactForm;
