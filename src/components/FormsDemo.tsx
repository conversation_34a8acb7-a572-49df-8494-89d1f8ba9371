import React, { useState } from 'react';
import { ArrowLeft, CheckCircle, Upload, MessageSquare } from 'lucide-react';
import { colors } from '../utils/colors';
import CustomForm from './CustomForm';
import NetlifyForm from './NetlifyForm';
import SimpleContactForm from './SimpleContactForm';

const FormsDemo: React.FC = () => {
  const [activeForm, setActiveForm] = useState<'custom' | 'netlify' | 'simple' | 'highlevel'>('netlify');

  const goHome = () => {
    window.location.href = '/';
  };

  const formOptions = [
    {
      id: 'netlify' as const,
      title: 'Netlify Form',
      description: 'Pure Netlify form handling with your Cloudflare image upload',
      icon: <CheckCircle className="w-5 h-5" />,
      features: ['Built-in spam protection', 'Form submissions in Netlify dashboard', 'No external dependencies', 'Image URL field for Cloudflare uploads']
    },
    {
      id: 'custom' as const,
      title: 'Custom Form (Current)',
      description: 'Your existing form with Cloudflare image upload + webhook',
      icon: <Upload className="w-5 h-5" />,
      features: ['Direct Cloudflare R2 image upload', 'HighLevel webhook integration', 'Google Places autocomplete', 'Advanced validation']
    },
    {
      id: 'simple' as const,
      title: 'Simple Contact Form',
      description: 'Basic Netlify contact form for simple inquiries',
      icon: <MessageSquare className="w-5 h-5" />,
      features: ['Clean, minimal design', 'Essential fields only', 'Fast loading', 'Mobile optimized']
    },
    {
      id: 'highlevel' as const,
      title: 'HighLevel Iframe',
      description: 'Embedded HighLevel form (current default)',
      icon: <CheckCircle className="w-5 h-5" />,
      features: ['Full HighLevel integration', 'Advanced lead tracking', 'Built-in CRM sync', 'Professional styling']
    }
  ];

  const renderActiveForm = () => {
    switch (activeForm) {
      case 'custom':
        return <CustomForm title="Custom Form Demo" />;
      case 'netlify':
        return <NetlifyForm title="Netlify Form Demo" />;
      case 'simple':
        return <SimpleContactForm title="Simple Contact Demo" />;
      case 'highlevel':
        return (
          <div className="w-full max-w-md mx-auto">
            <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
              <div className="px-6 py-4 text-center bg-gradient-to-r from-primary-600 to-primary-700">
                <h3 className="text-lg font-bold text-white">HighLevel Form Demo</h3>
              </div>
              <div className="h-[500px]">
                <iframe
                  src="https://api.leadconnectorhq.com/widget/form/SuFpnDAGDmAWtOUrKZQu"
                  className="w-full h-full border-0"
                  style={{
                    display: 'block',
                    borderRadius: '0 0 1rem 1rem',
                  }}
                  scrolling="no"
                  frameBorder="0"
                  title="HighLevel Form Demo"
                  loading="lazy"
                />
              </div>
            </div>
          </div>
        );
      default:
        return <NetlifyForm title="Netlify Form Demo" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <button
              onClick={goHome}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Site</span>
            </button>
            <h1 className="text-xl font-bold text-gray-900">Forms Demo & Testing</h1>
            <div className="w-24"></div> {/* Spacer for centering */}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Form Options for Your Site
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Test different form implementations to see which works best for your needs. 
            Each form has different features and integration capabilities.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Selection */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Choose a Form Type</h3>
            
            {formOptions.map((option) => (
              <div
                key={option.id}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  activeForm === option.id
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
                onClick={() => setActiveForm(option.id)}
              >
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    activeForm === option.id ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {option.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{option.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                    <ul className="mt-2 space-y-1">
                      {option.features.map((feature, index) => (
                        <li key={index} className="text-xs text-gray-500 flex items-center">
                          <CheckCircle className="w-3 h-3 text-green-500 mr-1" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}

            {/* Implementation Notes */}
            <div className="mt-8 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Implementation Notes</h4>
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>Netlify Forms:</strong> Automatically detected at build time. Submissions appear in your Netlify dashboard.</p>
                <p><strong>Custom Form:</strong> Uses your existing Cloudflare Workers for image uploads and HighLevel webhooks.</p>
                <p><strong>Image Uploads:</strong> For Netlify forms, users can upload to your Cloudflare service separately and paste the URL.</p>
              </div>
            </div>
          </div>

          {/* Form Preview */}
          <div className="lg:sticky lg:top-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Live Preview</h3>
            <div className="bg-white rounded-lg shadow-lg p-6">
              {renderActiveForm()}
            </div>
          </div>
        </div>

        {/* Integration Guide */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">How to Enable Netlify Forms</h3>
          <div className="prose max-w-none">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">1. Form Detection</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Netlify automatically detects forms with the <code className="bg-gray-100 px-1 rounded">netlify</code> attribute at build time.
                </p>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-x-auto">
{`<form name="contact" netlify>
  <input type="text" name="name" />
  <input type="email" name="email" />
  <button type="submit">Send</button>
</form>`}
                </pre>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">2. Spam Protection</h4>
                <p className="text-sm text-gray-600 mb-3">
                  Add honeypot fields and reCAPTCHA for enhanced security.
                </p>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-x-auto">
{`<form netlify netlify-honeypot="bot-field">
  <div style={{display: 'none'}}>
    <input name="bot-field" />
  </div>
  <!-- form fields -->
</form>`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormsDemo;
