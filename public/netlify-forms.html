<!DOCTYPE html>
<html>
<head>
    <title>Netlify Forms Detection</title>
    <meta name="robots" content="noindex">
</head>
<body>
    <!-- This file helps Netlify detect forms at build time -->
    <!-- These forms mirror the structure of your React forms -->
    
    <!-- Main Contact Form -->
    <form name="contact" netlify netlify-honeypot="bot-field" hidden>
        <input type="hidden" name="form-name" value="contact" />
        <input name="bot-field" />
        <input type="text" name="first_name" />
        <input type="text" name="last_name" />
        <input type="tel" name="phone" />
        <input type="email" name="email" />
        <input type="text" name="address" />
        <input type="text" name="city" />
        <input type="text" name="state" />
        <input type="text" name="country" />
        <input type="text" name="postal_code" />
        <input type="url" name="project_image_url" />
        <input type="checkbox" name="consent" />
        <textarea name="message"></textarea>
        <select name="project_type">
            <option value="deck">Deck Construction</option>
            <option value="bathroom">Bathroom Remodeling</option>
            <option value="repair">Home Repairs</option>
            <option value="handyman">Handyman Services</option>
            <option value="other">Other</option>
        </select>
    </form>

    <!-- Simple Contact Form -->
    <form name="simple-contact" netlify netlify-honeypot="bot-field" hidden>
        <input type="hidden" name="form-name" value="simple-contact" />
        <input name="bot-field" />
        <input type="text" name="name" />
        <input type="email" name="email" />
        <input type="tel" name="phone" />
        <textarea name="message"></textarea>
        <select name="service_interest">
            <option value="deck">Deck Construction</option>
            <option value="bathroom">Bathroom Remodeling</option>
            <option value="repair">Home Repairs</option>
            <option value="handyman">Handyman Services</option>
            <option value="consultation">Free Consultation</option>
        </select>
    </form>

    <!-- Newsletter Signup -->
    <form name="newsletter" netlify netlify-honeypot="bot-field" hidden>
        <input type="hidden" name="form-name" value="newsletter" />
        <input name="bot-field" />
        <input type="email" name="email" />
        <input type="text" name="name" />
    </form>
</body>
</html>
