# Netlify Forms Integration Guide

## Overview

Your site now supports multiple form options:

1. **Netlify Forms** - Native Netlify form handling (NEW)
2. **Custom Forms** - Your existing Cloudflare + HighLevel integration
3. **HighLevel Iframe** - Embedded HighLevel forms
4. **Simple Contact Forms** - Basic Netlify contact forms

## 🚀 Quick Start

### Testing Forms Locally
Visit `http://localhost:5173/forms` to test all form types in your development environment.

### Enabling Netlify Forms in Production

1. **Deploy to Netlify** - Forms are automatically detected at build time
2. **Check Form Detection** - Go to your Netlify dashboard → Site → Forms
3. **View Submissions** - All form submissions will appear in your Netlify dashboard

## 📋 Form Types Available

### 1. Netlify Form (Recommended for most use cases)
**File:** `src/components/NetlifyForm.tsx`

**Features:**
- ✅ Built-in spam protection (honeypot + reCAPTCHA)
- ✅ Form submissions in Netlify dashboard
- ✅ No external dependencies
- ✅ Image URL field for Cloudflare uploads
- ✅ Automatic form validation

**Usage:**
```tsx
import { NetlifyForm } from './components';

<NetlifyForm 
  title="Get Your Free Estimate"
  formName="contact"
  showImageUpload={true}
/>
```

### 2. Simple Contact Form
**File:** `src/components/SimpleContactForm.tsx`

**Features:**
- ✅ Clean, minimal design
- ✅ Essential fields only
- ✅ Fast loading
- ✅ Mobile optimized

**Usage:**
```tsx
import { SimpleContactForm } from './components';

<SimpleContactForm 
  title="Contact Us"
  className="max-w-lg mx-auto"
/>
```

### 3. Custom Form (Your Current Implementation)
**File:** `src/components/CustomForm.tsx`

**Features:**
- ✅ Direct Cloudflare R2 image upload
- ✅ HighLevel webhook integration
- ✅ Google Places autocomplete
- ✅ Advanced validation

**Usage:**
```tsx
import { CustomForm } from './components';

<CustomForm 
  title="Get Your Free Project Estimate"
  webhookUrl="your-webhook-url"
/>
```

## 🔧 Configuration

### Netlify Forms Configuration

The following files enable Netlify forms:

1. **`netlify.toml`** - Form detection enabled
```toml
[forms]
  enabled = true
```

2. **`public/netlify-forms.html`** - Static form detection
```html
<form name="contact" netlify netlify-honeypot="bot-field" hidden>
  <!-- Form fields for detection -->
</form>
```

3. **React Components** - Dynamic forms with Netlify attributes
```tsx
<form 
  name="contact" 
  method="POST" 
  netlify 
  netlify-honeypot="bot-field"
  onSubmit={handleSubmit}
>
```

### Image Upload Integration

For Netlify forms with image uploads:

1. **Option A: Image URL Field**
   - Users upload images to your Cloudflare service separately
   - Paste the generated URL into the form
   - Simple and reliable

2. **Option B: File Upload + Processing**
   - Add file input to Netlify form
   - Process uploaded files with Netlify Functions
   - Upload to Cloudflare R2 via serverless function

## 🎯 Switching Between Form Types

### In HeroSection Component

You can switch between form types using the `useCustomForm` prop:

```tsx
// Use Netlify Form
<HeroSection useCustomForm={false} />

// Use Custom Form (current)
<HeroSection useCustomForm={true} />
```

### Environment Variables

Update your `.env` file:
```bash
# For Custom Forms
VITE_HIGHLEVEL_WEBHOOK_URL=your-webhook-url
VITE_IMAGE_UPLOAD_URL=your-cloudflare-worker-url
VITE_IMAGE_UPLOAD_TOKEN=your-auth-token

# For Google Places (if using address autocomplete)
VITE_GOOGLE_PLACES_API_KEY=your-google-api-key
```

## 📊 Form Submissions

### Netlify Dashboard
- Go to your Netlify site dashboard
- Click "Forms" in the sidebar
- View all submissions, export data, set up notifications

### Custom Webhooks
- Your existing HighLevel integration continues to work
- Custom forms still send to your webhook endpoints
- Dual submission possible (Netlify + webhook)

## 🛡️ Spam Protection

### Netlify Forms Include:
1. **Honeypot fields** - Hidden fields that bots fill out
2. **reCAPTCHA integration** - Add `netlify-recaptcha` attribute
3. **Rate limiting** - Built-in protection against spam
4. **Custom validation** - Client-side and server-side validation

### Example with reCAPTCHA:
```tsx
<form 
  name="contact" 
  method="POST" 
  netlify 
  netlify-honeypot="bot-field"
  netlify-recaptcha
>
```

## 🚀 Deployment

### Build Process
1. Run `npm run build`
2. Netlify automatically detects forms in the built HTML
3. Forms become available immediately after deployment

### Verification
1. Check Netlify dashboard → Forms
2. Test form submissions
3. Verify submissions appear in dashboard

## 🔄 Migration Strategy

### Phase 1: Add Netlify Forms (Current)
- ✅ Netlify forms added alongside existing forms
- ✅ Test both systems in parallel
- ✅ No disruption to current workflow

### Phase 2: Choose Primary Form System
- Option A: Keep both (recommended)
- Option B: Migrate fully to Netlify forms
- Option C: Use Netlify for simple forms, Custom for complex forms

### Phase 3: Optimize
- Remove unused form components
- Streamline form handling
- Update documentation

## 📝 Next Steps

1. **Test the forms** at `/forms` route
2. **Deploy to Netlify** to enable form detection
3. **Check Netlify dashboard** for form submissions
4. **Choose your preferred form strategy**
5. **Update your main forms** based on testing results

## 🆘 Troubleshooting

### Forms Not Detected
- Ensure `netlify` attribute is present
- Check `netlify.toml` has forms enabled
- Verify static HTML forms in `public/netlify-forms.html`

### Submissions Not Appearing
- Check form `name` attribute matches
- Verify `form-name` hidden field is included
- Check Netlify dashboard for error messages

### Image Uploads
- For Netlify forms: Use URL field approach
- For Custom forms: Existing Cloudflare integration works
- Consider hybrid approach for best user experience
